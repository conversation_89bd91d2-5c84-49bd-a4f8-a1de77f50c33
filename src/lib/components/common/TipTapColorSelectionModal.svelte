<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import { FontColorOutline, CloseOutline, CheckOutline } from 'flowbite-svelte-icons';

    export let isOpen = false;
    export let selectedColor = 'black';
    // let colorOptions: Array<{ name: string; class: string; hex?: string }> = [];

    const dispatch = createEventDispatcher();

    // Color name to hex mapping for better visual representation
    const colorHexMap: Record<string, string> = {
        black: '#000000',
        gray: '#6b7280',
        red: '#ef4444',
        pink: '#ec4899',
        orange: '#f97316',
        yellow: '#eab308',
        purple: '#a855f7',
        violet: '#8b5cf6',
        blue: '#3b82f6',
        cyan: '#06b6d4',
        green: '#22c55e',
        lime: '#84cc16',
    };

    const colorOptions = [
        { name: "red", class: "bg-red" },
        { name: "orange", class: "bg-orange" },
        { name: "yellow", class: "bg-yellow" },
        { name: "pink", class: "bg-pink" },
        { name: "purple", class: "bg-purple" },
        { name: "violet", class: "bg-violet" },
        { name: "blue", class: "bg-blue" },
        { name: "cyan", class: "bg-cyan" },
        { name: "green", class: "bg-green" },
        { name: "lime", class: "bg-lime" },
        { name: "gray", class: "bg-gray" },
        { name: "black", class: "bg-black" }
    ];

    function selectColor(colorName: string) {
        dispatch('colorSelected', { color: colorName });
        closeModal();
    }

    function closeModal() {
        dispatch('close');
    }

    function handleBackdropClick(event: MouseEvent) {
        if (event.target === event.currentTarget) {
            closeModal();
        }
    }

    function handleBackdropKeydown(event: KeyboardEvent) {
        if (event.key === 'Escape') {
            closeModal();
        }
    }

    function handleKeydown(event: KeyboardEvent) {
        if (event.key === 'Escape') {
            closeModal();
        }
    }

    function getColorHex(colorName: string): string {
        return colorHexMap[colorName] || colorName;
    }
</script>

<svelte:window on:keydown={handleKeydown} />

{#if isOpen}
    <!-- Modal backdrop -->
    <!-- svelte-ignore a11y-no-noninteractive-element-interactions -->
    <!-- svelte-ignore a11y-click-events-have-key-events -->
    <div
        class="modal-backdrop"
        on:click={handleBackdropClick}
        on:keydown={handleBackdropKeydown}
        role="dialog"
        aria-modal="true"
        aria-labelledby="color-modal-title"
        tabindex="-1"
    >
        <!-- Modal content -->
        <div class="modal-content">
            <!-- Modal header -->
            <div class="modal-header">
                <div class="modal-title-container">
                    <FontColorOutline class="w-5 h-5 text-gray-600" />
                    <h3 id="color-modal-title" class="modal-title">Select Text Color</h3>
                </div>
                <button 
                    class="close-button" 
                    on:click={closeModal}
                    aria-label="Close color selection"
                >
                    <CloseOutline class="w-4 h-4" />
                </button>
            </div>

            <!-- Color grid -->
            <div class="color-grid">
                {#each colorOptions as color (color.name)}
                    <button
                        class="color-option"
                        class:selected={selectedColor === color.name}
                        style="background-color: {getColorHex(color.name)}"
                        on:click={() => selectColor(color.name)}
                        title="{color.name} ({getColorHex(color.name)})"
                        aria-label="Select {color.name} color"
                    >
                        {#if selectedColor === color.name}
                            <CheckOutline />
                        {/if}
                    </button>
                {/each}
            </div>

            <!-- Modal footer -->
            <!-- <div class="modal-footer">
                <button class="cancel-button" on:click={closeModal}>
                    Cancel
                </button>
            </div> -->
        </div>
    </div>
{/if}

<style>
    .modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        padding: 16px;
    }

    .modal-content {
        background: white;
        border-radius: 12px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        max-width: 320px;
        width: 100%;
        max-height: 90vh;
        overflow: hidden;
        animation: modalSlideIn 0.2s ease-out;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: scale(0.95) translateY(-10px);
        }
        to {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
    }

    .modal-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        border-bottom: 1px solid #e5e7eb;
    }

    .modal-title-container {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .modal-title {
        font-size: 16px;
        font-weight: 600;
        color: #111827;
        margin: 0;
    }

    .close-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 6px;
        border: none;
        background: transparent;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
    }

    .close-button:hover {
        background-color: #f3f4f6;
        color: #374151;
    }

    .color-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
        padding: 20px;
        gap: 10px;
    }

    .color-option {
        position: relative;
        width: 32px;
        height: 32px;
        border-radius: 8px;
        border: 2px solid transparent;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .color-option:hover {
        transform: scale(1.05);
        border-color: #d1d5db;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .color-option.selected {
        border-color: #374151;
        box-shadow: 0 0 0 2px #e5e7eb;
    }

    .selected-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.3);
    }

    .modal-footer {
        padding: 16px 20px;
        border-top: 1px solid #e5e7eb;
        display: flex;
        justify-content: flex-end;
    }

    .cancel-button {
        padding: 8px 16px;
        border-radius: 6px;
        border: 1px solid #d1d5db;
        background: white;
        color: #374151;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
    }

    .cancel-button:hover {
        background-color: #f9fafb;
        border-color: #9ca3af;
    }

    /* Responsive adjustments */
    @media (max-width: 480px) {
        .modal-content {
            max-width: 280px;
        }

        .color-grid {
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            padding: 16px;
        }

        .color-option {
            width: 44px;
            height: 44px;
        }
    }
</style>
